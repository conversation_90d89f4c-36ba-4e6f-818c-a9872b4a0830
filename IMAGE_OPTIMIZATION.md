# Image Optimization Guide

This project uses a single, comprehensive image optimization script that provides the best possible results for ecommerce applications while meeting Core Web Vitals requirements.

## Overview

The consolidated `scripts/optimize-images-sharp.js` script provides:

- **WebP with JPEG fallback support** - Creates modern WebP images while preserving original formats
- **Target file size optimization** - Adaptive quality reduction to achieve 30-100KB file sizes
- **Ecommerce-quality visuals** - Maintains visual quality suitable for product images
- **Core Web Vitals optimization** - Optimized for LCP (Largest Contentful Paint) performance
- **Responsive image variants** - Generates multiple sizes for different screen resolutions
- **Batch processing** - Efficiently handles multiple images with detailed statistics

## Quick Start

```bash
# Basic optimization (default: 80KB target, WebP + original optimization)
pnpm optimize-images

# Target smaller file sizes for better performance
pnpm optimize-images:target-50kb
pnpm optimize-images:target-30kb

# Generate responsive variants for different screen sizes
pnpm optimize-images:responsive

# Only create WebP versions (preserve originals unchanged)
pnpm optimize-images:webp-only

# Enable progressive JPEG encoding
pnpm optimize-images:progressive
```

## Advanced Usage

### Custom Configuration

```bash
# Custom source directory
node scripts/optimize-images-sharp.js --sourceDir=public/uploads

# Custom quality and target size
node scripts/optimize-images-sharp.js --quality=85 --target-size=60

# Responsive variants with custom sizes
node scripts/optimize-images-sharp.js --responsive --sizes=320,640,1024,1920

# Combined options
node scripts/optimize-images-sharp.js --target-size=50 --progressive --responsive
```

### Available Options

| Option | Description | Default |
|--------|-------------|---------|
| `--sourceDir=DIR` | Source directory | `public/images` |
| `--quality=N` | Initial quality level (1-100) | `80` |
| `--target-size=N` | Target file size in KB | `80` |
| `--webp-only` | Only create WebP versions | `false` |
| `--responsive` | Generate responsive variants | `false` |
| `--sizes=W1,W2,W3` | Responsive widths | `640,828,1200` |
| `--progressive` | Progressive JPEG encoding | `false` |
| `--help` | Show help message | - |

## Output Formats

### Standard Optimization
- **Original**: `image.jpg` (optimized)
- **WebP**: `image.webp` (optimized)

### Responsive Variants
- **WebP variants**: `image-640w.webp`, `image-828w.webp`, `image-1200w.webp`
- **JPEG fallbacks**: `image-640w.jpg`, `image-828w.jpg`, `image-1200w.jpg`

## Performance Features

### Adaptive Quality Reduction
The script automatically reduces quality in 10% increments until the target file size is achieved, ensuring optimal file sizes while maintaining visual quality.

### Advanced Compression Settings
- **JPEG**: MozJPEG encoder with optimized scans and trellis quantization
- **PNG**: Maximum compression with adaptive filtering and palette optimization
- **WebP**: High effort compression with smart subsampling

### Core Web Vitals Optimization
- Target file sizes under 100KB (ideally 30-80KB) for fast LCP
- Progressive JPEG support for perceived performance
- Responsive variants for optimal loading across devices

## Integration with Next.js

The generated responsive variants work seamlessly with Next.js Image component:

```jsx
import Image from 'next/image';

// Automatic format selection (WebP with JPEG fallback)
<Image
  src="/images/product.jpg"
  alt="Product"
  width={640}
  height={480}
  sizes="(max-width: 640px) 100vw, (max-width: 1200px) 50vw, 33vw"
/>
```

## File Size Guidelines

| Use Case | Target Size | Quality Range |
|----------|-------------|---------------|
| Hero images | 50-80KB | 75-85 |
| Product images | 30-60KB | 70-80 |
| Thumbnails | 10-30KB | 65-75 |
| Background images | 40-80KB | 70-80 |

## Best Practices

1. **Run optimization before deployment** to ensure consistent results
2. **Use responsive variants** for images larger than 640px width
3. **Target 50KB or less** for critical above-the-fold images
4. **Enable progressive JPEG** for large images to improve perceived performance
5. **Test on different devices** to ensure quality meets requirements

## Troubleshooting

### Common Issues

**Script doesn't run**: Ensure Sharp is installed
```bash
pnpm add sharp --save-dev
```

**Target size not reached**: Lower the target size or accept the warning
```bash
node scripts/optimize-images-sharp.js --target-size=100
```

**Images too small**: Increase quality or target size
```bash
node scripts/optimize-images-sharp.js --quality=90 --target-size=120
```

## Migration from Old Scripts

The following old scripts have been consolidated into the single solution:

- ❌ `optimize-images.js` - Replaced by `optimize-images-sharp.js`
- ❌ `optimize-large-webp.js` - Functionality integrated
- ❌ `image-tools.js` - Functionality integrated
- ❌ `delete-originals.js` - Removed (potentially dangerous)

All functionality is now available through the single script with better performance and more features.
